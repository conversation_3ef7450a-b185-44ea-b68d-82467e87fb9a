import React, { useState, useRef, useEffect } from 'react';
import {
    FaFacebook,
    FaXTwitter,
    FaYoutube,
    FaInstagram,
    FaWhatsapp,
    FaTelegram,
} from 'react-icons/fa6';
import logo from '../assets/logo-white.png';
import bguimg from '../assets/background.avif';
import motu from '../assets/img.avif';
import Swal from 'sweetalert2';

const HeroSection: React.FC = () => {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [error, setError] = useState('');
    const imageRef = useRef<HTMLImageElement | null>(null);

    const validatePhoneNumber = () => {
        const regex = /^(?:\+44|0)7\d{9}$/;
        if (!regex.test(phoneNumber)) {
            setError('Please enter a valid UK mobile number');
            return false;
        }
        return true;
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setError('');

        if (validatePhoneNumber()) {
            console.log('Submitting phone number:', phoneNumber);
            setPhoneNumber('');

            Swal.fire({
                title: 'Success!',
                text: 'You have successfully joined the waitlist.',
                icon: 'success',
                confirmButtonText: 'OK',
                confirmButtonColor: '#ED0CFF',
                background: '#321E2B',
                color: '#fff',
                customClass: {
                    popup: 'rounded-lg shadow-md',
                    title: 'text-xl font-semibold text-white',
                    htmlContainer: 'text-white',
                    confirmButton: 'uppercase font-bold',
                },
            });
        }
    };

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (!imageRef.current) return;

            const { innerWidth, innerHeight } = window;
            const offsetX = (e.clientX - innerWidth / 2) / 50;
            const offsetY = (e.clientY - innerHeight / 2) / 50;

            imageRef.current.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(1.05)`;
        };

        window.addEventListener('mousemove', handleMouseMove);
        return () => window.removeEventListener('mousemove', handleMouseMove);
    }, []);

    return (
        <section
            className="relative min-h-screen bg-cover bg-top flex items-center justify-start"
            style={{ backgroundImage: `url(${bguimg})` }}
        >
            <div className="absolute inset-0 bg-[linear-gradient(45deg,_#0000003b,_#5c525291)] z-0" />

            <div className="container mx-auto px-5 relative z-30">
                <div className="md:grid  md:grid-cols-2 items-center gap-6 ">
                    <div className="flex flex-col items-start justify-start w-full py-12 z-30">
                        <img src={logo} className="h-18 md:h-28" alt="logo" />
                        <div className='z-30'>
                            <p className="text-[2rem] bebas-font uppercase mt-10 mb-2 font-normal text-white tracking-normal" >
                                The streets are watching. Are you ready?
                            </p>
                            <p className="text-white">Rise from Hustler to Kingpin in Nigeria’s hottest, most addictive, reward-filled mobile game.</p>
                            {/* <p className="text-white">most addictive, reward-filled mobile game.</p> */}
                            <p className="text-white">
                                Play street games, earn Bucks, crypto, airtime, plus real rewards, limited edition merch & airtime.
                            </p>
                            <p className="text-white"></p>
                            <h4 className="text-white my-5 font-semibold capitalize font-bebas">
                                Hustlers move fast — join now & unlock early VIP crypto rewards!
                            </h4>
                        </div>

                        <form onSubmit={handleSubmit} className="w-full max-w-lg z-30">
                            <div className="flex overflow-hidden rounded-lg border border-white">
                                <input
                                    type="tel"
                                    placeholder="Your mobile number"
                                    className="bg-[#131313B2] text-white placeholder-gray-400 px-4 py-3 w-full focus:outline-none"
                                    value={phoneNumber}
                                    onChange={(e) => setPhoneNumber(e.target.value)}
                                />
                                <button
                                    type="submit"
                                    className="bg-white text-black font-bold bebas-font text-[15px] md:text-[20px]! px-4 uppercase whitespace-nowrap"
                                >
                                    Join Waitlist
                                </button>
                            </div>
                            {error && <p className="text-red-400 text-sm mt-2">{error}</p>}
                        </form>

                        <div className="mt-8 z-30">
                            <p className="mb-3 text-gray-300 tracking-wider font-bebas">
                                FOLLOW US ON SOCIAL MEDIA
                            </p>
                            <div className="flex flex-wrap justify-center gap-4 py-2">
                                <a
                                    href="https://facebook.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-[#1877F2b3] hover:bg-[#1877F2] w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                                >
                                    <FaFacebook size={24} className="text-white" />
                                </a>
                                <a
                                    href="https://x.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-[#000000b3] hover:bg-[#000000] w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                                >
                                    <FaXTwitter size={24} className="text-white" />
                                </a>
                                <a
                                    href="https://youtube.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-[#FF0000b3] hover:bg-[#ff0000] w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                                >
                                    <FaYoutube size={24} className="text-white" />
                                </a>
                                <a
                                    href="https://instagram.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-[#E1306Cb3] hover:bg-[#E1306C] w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                                >
                                    <FaInstagram size={24} className="text-white" />
                                </a>
                                <a
                                    href="https://whatsapp.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-[#25D366b3] hover:bg-[#25D366] w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                                >
                                    <FaWhatsapp size={24} className="text-white" />
                                </a>
                                <a
                                    href="https://telegram.org"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-[#229ED9b3] hover:bg-[#229ED9] w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                                >
                                    <FaTelegram size={24} className="text-white" />
                                </a>
                            </div>
                        </div>
                    </div>
                    <div className="absolute top-0 z-10 sm:overflow-hidden md:flex md:justify-end md:w-full ">
                        <img
                            src={motu}
                            alt="image"
                            ref={imageRef}
                            
                            className="transition-transform duration-100 ease-out opacity-40 md:opacity-100 md:w-full md:max-w-[500px]"
                        />
                    </div>
                </div>
            </div>
        </section>
    );
};

export default HeroSection;
