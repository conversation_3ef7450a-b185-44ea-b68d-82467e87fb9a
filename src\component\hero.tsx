import React, { useState } from 'react';
import facebookIcon from '../assets/social_Icons/facebook.svg';
import twitterIcon from '../assets/social_Icons/thread.svg';
import youtubeIcon from '../assets/social_Icons/youTube.svg';
import instagramIcon from '../assets/social_Icons/Instagram.svg';
// import whatsappIcon from '../assets/social_Icons/whatsapp.svg';
// import telegramIcon from '../assets/social_Icons/twitter.svg';
import logo from '../assets/logo-white.svg';
import bguimg from '../assets/background.avif';
import characterImage from '../assets/img.avif';
import Swal from 'sweetalert2';
import { registerUser } from './api';

const HeroSection: React.FC = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [error, setError] = useState('');

  const validatePhoneNumber = () => {
    const regex = /^[789]\d{9}$/;
    if (!regex.test(phoneNumber)) {
      setError('Please enter a valid 10-digit number starting with 7, 8, or 9');
      return false;
    }
    return true;  
  };


  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError('');

    if (validatePhoneNumber()) {
      try {
        // Only send number
        await registerUser({
          phone: phoneNumber
        });

        setPhoneNumber('');
        Swal.fire({
          title: 'Success!',
          text: 'You have successfully joined the waitlist.',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#ED0CFF',
          background: '#321E2B',
          color: '#fff',
          customClass: {
            popup: 'rounded-lg shadow-md',
            title: 'text-xl font-semibold text-white',
            htmlContainer: 'text-white',
            confirmButton: 'uppercase font-bold',
          },
        });
      } catch (err: any) {
        Swal.fire({
          title: 'Error!',
          text: err?.response?.data?.message || 'Failed to join waitlist. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#ED0CFF',
          background: '#321E2B',
          color: '#fff',
        });
      }
    }
  };

  return (
    <section
      className="relative min-h-screen bg-cover bg-top flex items-center justify-start"
      style={{ backgroundImage: `url(${bguimg})` }}
    >
      <div className="absolute inset-0 bg-[linear-gradient(45deg,_#0000007a,_#5c5252c2)] z-0" />

      <div className="container mx-auto px-2 relative z-30 ">
        <div className="md:grid md:grid-cols-2 items-center gap-6">
          <div className="flex flex-col items-start justify-start w-full py-12 z-30 px-4 sm:px-6 md:px-3 lg:px-8">
            <img
              src={logo}
              alt="logo"
              className="h-[200px] relative z-50"
            />

            <div className='z-10'>
              <p className="text-[36px] md:text-[45px] lg:text-[36px] bebas-font leading-[40px] tracking-[-0.01em] font-normal text-white pb-[48px] pt-[31px]" >
                The streets are watching. Are you ready?
              </p>
              <p className="text-white font-normal text-[1rem] pb-[16px]">Rise from Hustler to Kingpin in Nigeria's hottest, most addictive, <br /> reward-filled mobile game.</p>
              <p className="text-white pb-[16px]">
                Play street games, earn Bucks, crypto, airtime, plus real rewards, <br /> limited edition merch & airtime.
              </p>

              <h4 className="text-white font-semibold pb-[48px] capitalize font-bebas">
                Hustlers move fast — join now & unlock early VIP crypto rewards!
              </h4>
            </div>

            <form onSubmit={handleSubmit} className="w-full max-w-lg z-30 pb-12  ">
              <div className="flex overflow-hidden rounded-[8px] border border-white bg-black ">
                {/* +234 prefix box */}
                <div className="flex items-center px-4 border-r border-white/20 bg-white/10 text-white/80 rounded-l-[8px]">
                  +234
                </div>

                {/* Local 10-digit input */}
                <input
                  type="tel"
                  maxLength={10}
                  placeholder="8031234567"
                  className="flex-1 w-full px-4 py-3 text-white placeholder-gray-400 focus:outline-none rounded-none bg-black"
                  value={phoneNumber}
                  onChange={e => {
                    const digits = e.target.value.replace(/\D/g, '').slice(0, 10);
                    setPhoneNumber(digits);
                  }}
                />

                {/* Submit Button */}
                <button
                  type="submit"
                  className="flex items-center bg-white text-black font-[Antone] font-bold  bebas-font uppercase tracking-wide
        h-[42px] mt-auto mb-auto ml-0 px-4 rounded-[6px]
        text-[15px] sm:text-[16px] md:text-[13px] lg:text-[28px] mr-[3px]
        min-w-[100px] sm:min-w-[140px] md:min-w-[50px] lg:min-w-[160px]"
                >
                  Join Waitlist
                </button>
              </div>

              {error && <p className="mt-2 text-red-400 text-sm">{error}</p>}
            </form>



            <div className="z-30">
              {/* <p className="text-gray-300 tracking-wider font-bebas">
                FOLLOW US ON SOCIAL MEDIA
              </p> */}
              <div className="flex flex-wrap justify-center gap-4 py-2">
                <a
                  href="https://www.facebook.com/riseandhustlegame/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#092250] bg-opacity-35 w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                >
                  <img src={facebookIcon} alt="Facebook" className="w-6 h-6" />
                </a>
                <a
                  href="https://x.com/risehustlegame?s=11"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#252833]  bg-opacity-35  w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                >
                  <img src={twitterIcon} alt="X (Twitter)" className="w-6 h-6" />
                </a>
                <a
                  href="https://www.youtube.com/@riseandhustlegame"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#3f0f1a]  bg-opacity-35  w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                >
                  <img src={youtubeIcon} alt="YouTube" className="w-6 h-6" />
                </a>
                <a
                  href="https://www.instagram.com/riseandhustlegame/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#361742]  bg-opacity-35 w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                >
                  <img src={instagramIcon} alt="Instagram" className="w-6 h-6" />
                </a>
                {/* <a
                  href="https://whatsapp.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#173633]  bg-opacity-35 w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                >
                  <img src={whatsappIcon} alt="WhatsApp" className="w-6 h-6" />
                </a>
                <a
                  href="https://telegram.org"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#3a365f]  bg-opacity-35 w-[65px] flex justify-center p-3 rounded-[8px] transition-colors"
                >
                  <img src={telegramIcon} alt="Telegram" className="w-6 h-6" />
                </a> */}
              </div>
            </div>
          </div>

          {/* FIXED CHARACTER IMAGE CONTAINER */}
          <div className="absolute top-0 right-0 bottom-0 z-10 md:relative md:flex md:justify-end ">
            <img
              src={characterImage}
              alt="Rise & Muscle logo"
              className="hidden  md:block  opacity-40 w-auto object-center md:opacity-100  md:min-h-screen"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;