import axios from 'axios';

const api = axios.create({
  baseURL: 'https://backend.riseandhustle.io',
});

/**
 * Registers a new user with phone number only.
 *
 * @param {Object} data - Registration data.
 * @param {string} data.phone
 - The user's phone number.
 * @returns {Promise<Object>} A promise that resolves to the registration response.
 */
export const registerUser = async (data: { phone: string }) => {
  if (!data || !data.phone) {
    throw new Error("number is required for registerUser");
  }
  const response = await api.post("/api/v1/register", data);
  return response.data;
};

export default api;
